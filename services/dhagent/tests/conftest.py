"""Pytest configuration and fixtures."""

import asyncio
from collections.abc import Generator

import pytest
from fastapi.testclient import TestClient

from src.entities.alert import <PERSON><PERSON>, AlertSeverity
from src.external.robot.robot import (
    AddMembersRequest,
    CreateConversationRequest,
    IDType,
    SearchType,
    SendMessageRequest,
    ServiceCredential,
    UpdateConversationRequest,
    UserCredentialsRequest,
)
from src.interfaces.api.app import app


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> TestClient:
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
def sample_alert() -> Alert:
    """Create a sample Alert for testing."""
    return Alert(
        id="test-alert-123",
        name="Sample Test Alert",
        summary="This is a sample alert for testing purposes",
        severity=AlertSeverity.MEDIUM,
        service="test-service",
        environment="test",
    )


@pytest.fixture
def sample_alert_data() -> dict:
    """Create sample alert data for API testing."""
    return {
        "name": "API Test Alert",
        "summary": "Alert data for API testing",
        "severity": "high",
        "service": "api-service",
        "environment": "staging",
    }


# Robot-specific fixtures
@pytest.fixture
def mock_idaas_token() -> str:
    """Mock IDaaS token for testing."""
    return "mock_idaas_token_12345"


@pytest.fixture
def sample_robot_user_credentials_request() -> UserCredentialsRequest:
    """Sample user credentials request for conversation testing."""
    return UserCredentialsRequest(
        search_type=SearchType.LDAP_NAME,
        user_id="testuser",
        services=[
            ServiceCredential(id="service1", scopes=["read", "write"]),
            ServiceCredential(id="service2", scopes=["admin"]),
        ],
    )


@pytest.fixture
def sample_robot_create_conversation_request() -> CreateConversationRequest:
    """Sample create conversation request for conversation testing."""
    return CreateConversationRequest(
        title="Test Conversation",
        scenario="deepheal",
        metadata={"members": {"id_type": "ldap_name", "ids": ["user1", "user2"]}},
    )


@pytest.fixture
def sample_robot_add_members_request() -> AddMembersRequest:
    """Sample add members request for conversation testing."""
    return AddMembersRequest(id_type=IDType.LDAP_NAME, ids=["user3", "user4"])


@pytest.fixture
def sample_robot_update_conversation_request() -> UpdateConversationRequest:
    """Sample update conversation request for conversation testing."""
    return UpdateConversationRequest(title="Updated Title")


@pytest.fixture
def sample_robot_send_message_request() -> SendMessageRequest:
    """Sample send message request for conversation testing."""
    return SendMessageRequest(
        parent_id=None,
        messages=[
            {
                "sender": {"role": "agent", "name": "dhagent"},
                "recipient": {"role": "notifier", "name": ""},
                "contents": [{"mime_type": "text/plain", "body": {"text": "Hello"}}],
            }
        ],
    )
