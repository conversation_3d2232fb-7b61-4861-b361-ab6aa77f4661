from logging import getLogger
from typing import cast

from cloudevents.pydantic import CloudEvent
from langgraph.types import Command

from src.entities.resume_event import UserResumeEventData
from src.external.licloud.cicd import OAMAppDeployRunningSucceedEventData
from src.external.licloud.langfuse import langfuse_handler
from src.usecases.event_distribution.base import CloudEventHandler

from .change_manager import (
    AgenticChangeManager,
    ChangeManagerState,
)
from .state import AgentMode

logger = getLogger(__name__)


class ChangeEventHandler(CloudEventHandler):
    """
    Receives change events and triggers the agent.
    """

    change_manager = AgenticChangeManager()

    # Supported event types that this handler can process
    SUPPORTED_EVENT_TYPES = {
        "cloud.chj.li.oam.app.deploy.running.succeed",
        "user.resume.conversation",  # 用户恢复中断
        # Add more event types here as needed
    }

    async def should_handle(self, event: CloudEvent) -> bool:
        """
        Determines if the event should be handled by this handler.

        Args:
            event CloudEvent: The event to check.

        Returns:
            bool: True if the event should be handled, False otherwise.
        """
        logger.info(f"ChangeEventHandler checking event type: {event.type}")
        return event.type in self.SUPPORTED_EVENT_TYPES

    async def handle_event(self, event: CloudEvent) -> None:
        """
        Handles different types of events.

        Args:
            event CloudEvent: The event to handle.
        """
        logger.info(f"Handling event: {event.type}")

        if event.type == "cloud.chj.li.oam.app.deploy.running.succeed":
            await self._handle_deploy_event(event)
        elif event.type == "user.resume.conversation":
            await self._handle_resume_event(event)
        else:
            logger.warning(f"Unsupported event type: {event.type}")

    async def _handle_deploy_event(self, event: CloudEvent) -> None:
        """处理部署事件"""
        logger.info(f"Handling deploy event: {event}")
        deploy_running_event = OAMAppDeployRunningSucceedEventData.model_validate(event.data)
        thread_id = "abcd"  # hardcode here for test

        change_manager = self.change_manager.graph
        result = cast(
            "ChangeManagerState",
            await change_manager.ainvoke(
                input={
                    # TODO: support application-level diagnosis @zhenghe
                    "component": deploy_running_event.data.components[0],
                    "history": deploy_running_event.data.history,
                    "requested_mode": AgentMode.DIAGNOSIS,
                },
                config={
                    "configurable": {
                        "thread_id": thread_id,
                    },
                    "recursion_limit": 50,
                    "callbacks": [langfuse_handler],
                },
            ),
        )

        logger.info(f"Change event handled, thread_id: {thread_id}, result: {result}")

    async def _handle_resume_event(self, event: CloudEvent) -> None:
        """处理用户恢复中断事件"""
        logger.info(f"Handling resume event: {event}")

        resume_event = UserResumeEventData.model_validate(event.data)
        thread_id = str(resume_event.metadata.conversation_id)

        # 恢复会话
        change_manager = self.change_manager.graph
        result = cast(
            "ChangeManagerState",
            await change_manager.ainvoke(
                Command(resume=resume_event.content.parts[0].data),
                config={
                    "configurable": {
                        "thread_id": thread_id,
                    },
                    "recursion_limit": 50,
                    "callbacks": [langfuse_handler],
                },
            ),
        )

        logger.info(f"Resume event handled, thread_id: {thread_id}, result: {result}")
