from abc import ABCMeta
from typing import Any


class RobotConversationServiceSingletonABCMeta(ABCMeta):
    """
    Combined metaclass for singleton classes that also need ABC functionality.

    Inherits from ABCMeta to support abstract base classes while providing singleton behavior.
    """

    _instances: dict[type, Any] = {}

    def __call__(cls, *args: Any, **kwargs: Any) -> Any:
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]