"""Robot service - Use cases for conversation operations."""

import logging

from src.external.robot.http_client import RobotHttpClient
from src.external.robot.robot import (
    AddMembersRequest,
    AddMembersResponse,
    ConversationHistoryResponse,
    ConversationResponse,
    CreateConversationRequest,
    IDType,
    SearchType,
    SendMessageRequest,
    SendMessageResponse,
    ServiceCredential,
    UpdateConversationRequest,
    UserCredentialsRequest,
    UserCredentialsResponse,
)
from src.usecases.shared.conversation_service import ConversationService

logger = logging.getLogger(__name__)


class RobotConversationService(ConversationService):
    """
    Robot service containing business logic for conversation operations.

    This service orchestrates conversation operations and implements business rules,
    following Clean Architecture principles.
    """

    def __init__(self, http_client: RobotHttpClient) -> None:
        """
        Initialize conversation service.

        Args:
            http_client: Robot HTTP client for external API communication
        """
        self._http_client = http_client

    async def create_conversation(
        self, title: str, ids: list[str], scenario: str = "deepheal", id_type: IDType = IDType.LDAP_NAME
    ) -> ConversationResponse:
        """
        Create a new conversation conversation.

        Args:
            title: Conversation title
            ids: List of member IDs to include
            scenario: Conversation scenario (default: "deepheal")
            id_type: Type of member identifiers

        Returns:
            Created conversation response

        Raises:
            RobotConversationServiceError: If the operation fails
        """
        try:
            # Build metadata with member information
            metadata = {"members": {"id_type": id_type.value, "ids": ids}}

            request = CreateConversationRequest(title=title, scenario=scenario, metadata=metadata)

            logger.info(f"Creating conversation '{title}' with {len(ids)} members")

            response = await self._http_client.create_conversation(request)

            logger.info(f"Successfully created conversation {response.data.id}: '{title}'")
            return response

        except Exception as e:
            logger.error(f"Failed to create conversation '{title}': {e}")
            raise RobotConversationServiceError(f"Failed to create conversation: {e}") from e

    async def invite_users_to_conversation(
        self, conversation_id: int, ids: list[str], id_type: IDType = IDType.LDAP_NAME
    ) -> AddMembersResponse:
        """
        Invite users to join an existing conversation.

        Args:
            conversation_id: Target conversation ID
            ids: List of member IDs to invite
            id_type: Type of member identifiers

        Returns:
            Members addition response

        Raises:
            RobotConversationServiceError: If the operation fails
        """
        try:
            request = AddMembersRequest(id_type=id_type, ids=ids)

            logger.info(f"Inviting {len(ids)} users to conversation {conversation_id}")

            response = await self._http_client.add_conversation_members(conversation_id, request)

            invalid_count = len(response.data.get("invalid_id_list", []))
            if invalid_count > 0:
                logger.warning(f"Conversation {conversation_id}: {invalid_count} invalid member IDs")

            logger.info(f"Successfully processed invitation for conversation {conversation_id}")
            return response

        except Exception as e:
            logger.error(f"Failed to invite users to conversation {conversation_id}: {e}")
            raise RobotConversationServiceError(f"Failed to invite users: {e}") from e

    async def update_conversation_title(self, conversation_id: int, title: str) -> ConversationResponse:
        """
        Update conversation title.

        Args:
            conversation_id: Target conversation ID
            title: New conversation title

        Returns:
            Updated conversation response

        Raises:
            RobotConversationServiceError: If the operation fails
        """
        try:
            request = UpdateConversationRequest(title=title)

            logger.info(f"Updating conversation {conversation_id} title to '{title}'")

            response = await self._http_client.update_conversation(conversation_id, request)

            logger.info(f"Successfully updated conversation {conversation_id} title")
            return response

        except Exception as e:
            logger.error(f"Failed to update conversation {conversation_id} title: {e}")
            raise RobotConversationServiceError(f"Failed to update conversation: {e}") from e

    async def send_conversation_message(
        self, conversation_id: int, messages: list[dict], parent_id: int | None = None
    ) -> SendMessageResponse:
        """
        Send a message to a conversation.

        Args:
            conversation_id: Target conversation ID
            messages: List of message objects to send
            parent_id: Optional parent message ID

        Returns:
            Message sending response

        Raises:
            RobotConversationServiceError: If the operation fails
        """
        try:
            request = SendMessageRequest(parent_id=parent_id, messages=messages)

            logger.info(f"Sending {len(messages)} message(s) to conversation {conversation_id}")

            response = await self._http_client.send_message(conversation_id, request)

            logger.info(f"Successfully sent message(s) to conversation {conversation_id}")
            return response

        except Exception as e:
            logger.error(f"Failed to send message to conversation {conversation_id}: {e}")
            raise RobotConversationServiceError(f"Failed to send message: {e}") from e

    async def get_conversation_history(self, conversation_id: int) -> ConversationHistoryResponse:
        """
        Get conversation history.

        Args:
            conversation_id: Target conversation ID

        Returns:
            Conversation history response

        Raises:
            RobotConversationServiceError: If the operation fails
        """
        try:
            logger.info(f"Getting history for conversation {conversation_id}")

            response = await self._http_client.get_conversation_history(conversation_id)

            return response

        except Exception as e:
            logger.error(f"Failed to get conversation {conversation_id} history: {e}")
            raise RobotConversationServiceError(f"Failed to get conversation history: {e}") from e


class RobotConversationServiceError(Exception):
    """Exception raised by conversation service operations."""

    def __init__(self, message: str, cause: Exception | None = None):
        super().__init__(message)
        self.message = message
        self.cause = cause
