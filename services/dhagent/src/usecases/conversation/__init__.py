"""Robot use cases package."""

from src.config import settings
from src.external.idaas.client import IDaaSClient
from src.external.robot.http_client import RobotHttpClient
from src.usecases.shared.conversation_service import ConversationService

from .robot_conversation import RobotConversationService, RobotConversationServiceError

__all__ = [
    "ConversationService",
    "RobotConversationService",
    "RobotConversationServiceError",
    "default_robot_service",
]


class RobotConversationServiceManager:
    """Provider for default conversation service instance with lazy initialization."""

    def __init__(self) -> None:
        self._service: ConversationService | None = None

    def get_service(self) -> ConversationService:
        """Get or create the default conversation service instance."""
        if self._service is None:
            self._service = self._create_service()
        return self._service

    def _create_service(self) -> ConversationService:
        """Create conversation service with all dependencies."""
        # Create and configure IDaaS client
        idaas_client = IDaaSClient()
        idaas_client.initialize(
            client_id=settings.idaas_dhagent_client_id,
            client_secret=settings.idaas_dhagent_client_secret,
            service_id=settings.idaas_robot_service_id,
            endpoint=settings.idaas_host,
            scopes=settings.idaas_robot_service_scopes,
        )

        # Create HTTP client
        http_client = RobotHttpClient(
            idaas_client=idaas_client, base_url=settings.robot_api_base_url, timeout=settings.robot_api_timeout
        )

        # Create and return service
        return RobotConversationService(http_client)


manager = RobotConversationServiceManager()
default_robot_service = manager.get_service()
